# Simple VPC Creation Guide

This guide explains how to use the `simple_vpc` role to create AWS VPC infrastructure using Terraform and Ansible integration with S3 backend state storage.

## Overview

The `simple_vpc` role provides:
- **Simple VPC creation** with public and private subnets
- **Terraform and Ansible integration** for infrastructure as code
- **S3 backend** for Terraform state storage (no DynamoDB)
- **Self-contained role** with no external dependencies
- **Role-specific variables** only (no common role variables)

## Architecture

The role creates the following AWS resources:
- 1 VPC with configurable CIDR
- 2 Public subnets (with Internet Gateway access)
- 2 Private subnets (with NAT Gateway access)
- 1 Internet Gateway
- 2 NAT Gateways (one per AZ for high availability)
- Route tables and associations
- Elastic IPs for NAT Gateways

## Prerequisites

1. **AWS CLI** configured with appropriate permissions
2. **Terraform** >= 1.0 installed
3. **Ansible** >= 2.12 installed
4. **AWS Ansible Collection**:
   ```bash
   ansible-galaxy collection install amazon.aws
   ```

## Required AWS Permissions

Your AWS credentials need the following permissions:
- EC2: VPC, Subnet, Internet Gateway, NAT Gateway, Route Table management
- S3: Bucket creation and object management
- IAM: Basic read permissions for AWS account information

## Quick Start

### 1. Basic Usage

Create a playbook with minimal configuration:

```yaml
---
- name: Deploy Simple VPC
  hosts: localhost
  vars:
    terraform_state_bucket: "my-vpc-state-bucket"
  tasks:
    - include_role:
        name: simple_vpc
```

### 2. Custom Configuration

```yaml
---
- name: Deploy Custom VPC
  hosts: localhost
  vars:
    terraform_state_bucket: "my-custom-vpc-state"
    vpc_name: "production-vpc"
    vpc_cidr: "********/16"
    aws_region: "us-west-2"
    environment: "production"
  tasks:
    - include_role:
        name: simple_vpc
```

## Configuration Variables

### Required Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `terraform_state_bucket` | S3 bucket for Terraform state | `"my-terraform-state"` |

### Optional Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `vpc_name` | `"simple-vpc"` | Name of the VPC |
| `vpc_cidr` | `"10.0.0.0/16"` | CIDR block for VPC |
| `aws_region` | `"us-east-1"` | AWS region |
| `environment` | `"dev"` | Environment tag |
| `public_subnet_cidrs` | `["********/24", "********/24"]` | Public subnet CIDRs |
| `private_subnet_cidrs` | `["*********/24", "*********/24"]` | Private subnet CIDRs |
| `availability_zones` | `["us-east-1a", "us-east-1b"]` | Availability zones |
| `terraform_state_key` | `"simple-vpc/terraform.tfstate"` | S3 key for state file |

## File Structure

```
roles/simple_vpc/
├── README.md                           # Role documentation
├── meta/
│   └── main.yml                       # Role metadata
├── vars/
│   └── main.yml                       # Default variables
├── tasks/
│   ├── main.yml                       # Main task orchestration
│   ├── create_s3_bucket.yml          # S3 bucket creation
│   └── deploy_vpc.yml                 # Terraform deployment
├── templates/
│   ├── main.tf.j2                     # Main Terraform config
│   ├── backend.tf.j2                  # S3 backend config
│   ├── variables.tf.j2                # Terraform variables
│   └── outputs.tf.j2                  # Terraform outputs
└── terraform/
    └── modules/
        └── simple_vpc/
            ├── main.tf                # VPC resources
            ├── variables.tf           # Module variables
            ├── outputs.tf             # Module outputs
            └── versions.tf            # Provider versions
```

## Usage Examples

### Example 1: Development Environment

```bash
ansible-playbook examples/simple_vpc_playbook.yml
```

### Example 2: Production Environment

```yaml
---
- name: Production VPC
  hosts: localhost
  vars:
    terraform_state_bucket: "prod-terraform-state"
    vpc_name: "production-vpc"
    vpc_cidr: "**********/16"
    aws_region: "us-east-1"
    environment: "production"
    public_subnet_cidrs:
      - "**********/24"
      - "**********/24"
    private_subnet_cidrs:
      - "***********/24"
      - "***********/24"
    common_tags:
      Environment: "production"
      Project: "main-app"
      Owner: "platform-team"
      CostCenter: "engineering"
  tasks:
    - include_role:
        name: simple_vpc
```

## Outputs

After successful deployment, the role provides these Terraform outputs:
- `vpc_id`: VPC identifier
- `vpc_cidr_block`: VPC CIDR block
- `public_subnet_ids`: List of public subnet IDs
- `private_subnet_ids`: List of private subnet IDs
- `internet_gateway_id`: Internet Gateway ID
- `nat_gateway_ids`: List of NAT Gateway IDs
- `nat_gateway_public_ips`: List of NAT Gateway public IPs

## Cleanup

To destroy the infrastructure:

```bash
ansible-playbook examples/cleanup_vpc_playbook.yml
```

Or manually:
```bash
cd /tmp/simple_vpc_terraform
terraform destroy
```

## Key Features

1. **No DynamoDB**: Uses S3-only backend as requested
2. **Self-contained**: No dependencies on common roles
3. **Role variables only**: All variables defined within the role
4. **Automatic S3 bucket creation**: Creates state bucket if it doesn't exist
5. **High availability**: NAT Gateways in multiple AZs
6. **Secure by default**: Private subnets with NAT Gateway access

## Troubleshooting

### Common Issues

1. **S3 bucket already exists**: The role will use existing bucket
2. **Insufficient permissions**: Ensure AWS credentials have required permissions
3. **Terraform not found**: Install Terraform >= 1.0
4. **Region mismatch**: Ensure availability zones match the AWS region

### Debug Mode

Run with verbose output:
```bash
ansible-playbook -vvv examples/simple_vpc_playbook.yml
```

## Security Considerations

- S3 bucket is created with encryption and public access blocked
- Private subnets use NAT Gateways for outbound internet access
- No state locking (DynamoDB) as requested - consider implications for team usage
- Terraform state contains sensitive information - secure S3 bucket appropriately
