#############################################################################################
#                                                                                           #
# Title:        vpc_main.tf                                                                 #
# Version:                                                                                  #
#               2021-11-09 WRC. Add provider def to eliminate warnings in latest version    #
#               2020-12-07 WRC. Add outputs                                                 #
#               2020-11-10 WRC. Converge variables and outputs in this file.                #
#               2020-06-09 WRC. Initial                                                     #
# Create Date:  2020-06-09                                                                  #
# Author:       <PERSON><PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                          #
# Description:                                                                              #
#               Established the terraform state this account and environment                #
#                                                                                           #
#############################################################################################
# terraform {
#   required_providers { aws = {} }
# }

#-------------------------------------------------------------------------------------------#
# Locals Section                                                                            #
#-------------------------------------------------------------------------------------------#
variable "account"      { type = string }
variable "region"       { type = string }
variable "environment"  { type = string }
variable "vpc_init"     {
                          type = bool
                          default = false
                        }
variable "common_data" {}

#-------------------------------------------------------------------------------------------#
# Data Section                                                                              #
#-------------------------------------------------------------------------------------------#

#-------------------------------------------------------------------------------------------#
# Outputs Section                                                                           #
#-------------------------------------------------------------------------------------------#
# output "vpc_data" {
#   description = "The set of output data to make available to modules."
#   sensitive   = false
#   value = {
#     aws_subnet_public = aws_subnet.public
#   }
# }