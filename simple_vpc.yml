---
# Simple VPC Creation Playbook
# Creates a VPC with public/private subnets using Terraform

- name: Create Simple VPC
  hosts: localhost
  gather_facts: false
  vars:
    # VPC Configuration
    vpc_name: "simple-vpc"
    vpc_cidr: "10.0.0.0/16"
    aws_region: "us-east-1"
    environment: "dev"

    # Subnet Configuration
    public_subnet_cidrs:
      - "********/24"
      - "********/24"
    private_subnet_cidrs:
      - "*********/24"
      - "*********/24"
    availability_zones:
      - "us-east-1a"
      - "us-east-1b"

    # Working Directory
    terraform_dir: "/tmp/simple_vpc"

  tasks:
    - name: Create Terraform working directory
      ansible.builtin.file:
        path: "{{ terraform_dir }}"
        state: directory
        mode: '0755'

    - name: Create Terraform main configuration
      ansible.builtin.copy:
        dest: "{{ terraform_dir }}/main.tf"
        content: |
          terraform {
            required_version = ">= 1.0"
            required_providers {
              aws = {
                source  = "hashicorp/aws"
                version = "~> 5.0"
              }
            }
          }

          provider "aws" {
            region = "{{ aws_region }}"
          }

          # VPC
          resource "aws_vpc" "main" {
            cidr_block           = "{{ vpc_cidr }}"
            enable_dns_hostnames = true
            enable_dns_support   = true
            tags = {
              Name        = "{{ vpc_name }}"
              Environment = "{{ environment }}"
            }
          }

          # Internet Gateway
          resource "aws_internet_gateway" "main" {
            vpc_id = aws_vpc.main.id
            tags = {
              Name = "{{ vpc_name }}-igw"
            }
          }

          # Public Subnets
          resource "aws_subnet" "public" {
            count = 2
            vpc_id                  = aws_vpc.main.id
            cidr_block              = "{{ public_subnet_cidrs[0] }}"
            availability_zone       = "{{ availability_zones[0] }}"
            map_public_ip_on_launch = true
            tags = {
              Name = "{{ vpc_name }}-public-${count.index + 1}"
              Type = "Public"
            }
          }

          resource "aws_subnet" "public_2" {
            vpc_id                  = aws_vpc.main.id
            cidr_block              = "{{ public_subnet_cidrs[1] }}"
            availability_zone       = "{{ availability_zones[1] }}"
            map_public_ip_on_launch = true
            tags = {
              Name = "{{ vpc_name }}-public-2"
              Type = "Public"
            }
          }

          # Private Subnets
          resource "aws_subnet" "private" {
            vpc_id            = aws_vpc.main.id
            cidr_block        = "{{ private_subnet_cidrs[0] }}"
            availability_zone = "{{ availability_zones[0] }}"
            tags = {
              Name = "{{ vpc_name }}-private-1"
              Type = "Private"
            }
          }

          resource "aws_subnet" "private_2" {
            vpc_id            = aws_vpc.main.id
            cidr_block        = "{{ private_subnet_cidrs[1] }}"
            availability_zone = "{{ availability_zones[1] }}"
            tags = {
              Name = "{{ vpc_name }}-private-2"
              Type = "Private"
            }
          }

          # Elastic IPs for NAT Gateways
          resource "aws_eip" "nat_1" {
            domain = "vpc"
            depends_on = [aws_internet_gateway.main]
            tags = {
              Name = "{{ vpc_name }}-nat-eip-1"
            }
          }

          resource "aws_eip" "nat_2" {
            domain = "vpc"
            depends_on = [aws_internet_gateway.main]
            tags = {
              Name = "{{ vpc_name }}-nat-eip-2"
            }
          }

          # NAT Gateways
          resource "aws_nat_gateway" "nat_1" {
            allocation_id = aws_eip.nat_1.id
            subnet_id     = aws_subnet.public[0].id
            tags = {
              Name = "{{ vpc_name }}-nat-1"
            }
            depends_on = [aws_internet_gateway.main]
          }

          resource "aws_nat_gateway" "nat_2" {
            allocation_id = aws_eip.nat_2.id
            subnet_id     = aws_subnet.public_2.id
            tags = {
              Name = "{{ vpc_name }}-nat-2"
            }
            depends_on = [aws_internet_gateway.main]
          }

          # Public Route Table
          resource "aws_route_table" "public" {
            vpc_id = aws_vpc.main.id
            route {
              cidr_block = "0.0.0.0/0"
              gateway_id = aws_internet_gateway.main.id
            }
            tags = {
              Name = "{{ vpc_name }}-public-rt"
            }
          }

          # Private Route Tables
          resource "aws_route_table" "private_1" {
            vpc_id = aws_vpc.main.id
            route {
              cidr_block     = "0.0.0.0/0"
              nat_gateway_id = aws_nat_gateway.nat_1.id
            }
            tags = {
              Name = "{{ vpc_name }}-private-rt-1"
            }
          }

          resource "aws_route_table" "private_2" {
            vpc_id = aws_vpc.main.id
            route {
              cidr_block     = "0.0.0.0/0"
              nat_gateway_id = aws_nat_gateway.nat_2.id
            }
            tags = {
              Name = "{{ vpc_name }}-private-rt-2"
            }
          }

          # Route Table Associations
          resource "aws_route_table_association" "public_1" {
            subnet_id      = aws_subnet.public[0].id
            route_table_id = aws_route_table.public.id
          }

          resource "aws_route_table_association" "public_2" {
            subnet_id      = aws_subnet.public_2.id
            route_table_id = aws_route_table.public.id
          }

          resource "aws_route_table_association" "private_1" {
            subnet_id      = aws_subnet.private.id
            route_table_id = aws_route_table.private_1.id
          }

          resource "aws_route_table_association" "private_2" {
            subnet_id      = aws_subnet.private_2.id
            route_table_id = aws_route_table.private_2.id
          }

          # Outputs
          output "vpc_id" {
            value = aws_vpc.main.id
          }

          output "public_subnet_ids" {
            value = [aws_subnet.public[0].id, aws_subnet.public_2.id]
          }

          output "private_subnet_ids" {
            value = [aws_subnet.private.id, aws_subnet.private_2.id]
          }

    - name: Initialize Terraform
      ansible.builtin.command:
        cmd: terraform init
        chdir: "{{ terraform_dir }}"

    - name: Plan Terraform deployment
      ansible.builtin.command:
        cmd: terraform plan
        chdir: "{{ terraform_dir }}"

    - name: Apply Terraform configuration
      ansible.builtin.command:
        cmd: terraform apply -auto-approve
        chdir: "{{ terraform_dir }}"

    - name: Get Terraform outputs
      ansible.builtin.command:
        cmd: terraform output -json
        chdir: "{{ terraform_dir }}"
      register: terraform_outputs

    - name: Display VPC information
      ansible.builtin.debug:
        msg:
          - "VPC created successfully!"
          - "VPC ID: {{ (terraform_outputs.stdout | from_json).vpc_id.value }}"
          - "Public Subnets: {{ (terraform_outputs.stdout | from_json).public_subnet_ids.value }}"
          - "Private Subnets: {{ (terraform_outputs.stdout | from_json).private_subnet_ids.value }}"