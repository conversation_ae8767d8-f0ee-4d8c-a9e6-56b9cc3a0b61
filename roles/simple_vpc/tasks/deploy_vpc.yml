---
# Deploy VPC using Terraform

- name: Create Terraform working directory
  ansible.builtin.file:
    path: "{{ terraform_work_dir }}"
    state: directory
    mode: '0755'

- name: Copy Terraform module to working directory
  ansible.builtin.copy:
    src: "{{ role_path }}/terraform/modules/"
    dest: "{{ terraform_work_dir }}/modules/"
    mode: '0644'

- name: Generate Terraform configuration files
  ansible.builtin.template:
    src: "{{ item.src }}"
    dest: "{{ terraform_work_dir }}/{{ item.dest }}"
    mode: '0644'
  loop:
    - { src: "main.tf.j2", dest: "main.tf" }
    - { src: "variables.tf.j2", dest: "variables.tf" }
    - { src: "outputs.tf.j2", dest: "outputs.tf" }

- name: Initialize Terraform
  ansible.builtin.command:
    cmd: terraform init
    chdir: "{{ terraform_work_dir }}"
  register: terraform_init
  changed_when: "'Terraform has been successfully initialized' in terraform_init.stdout"

- name: Validate Terraform configuration
  ansible.builtin.command:
    cmd: terraform validate
    chdir: "{{ terraform_work_dir }}"
  register: terraform_validate
  changed_when: false

- name: Plan Terraform deployment
  ansible.builtin.command:
    cmd: terraform plan -out=tfplan
    chdir: "{{ terraform_work_dir }}"
  register: terraform_plan
  changed_when: "'No changes' not in terraform_plan.stdout"

- name: Apply Terraform configuration
  ansible.builtin.command:
    cmd: terraform apply -auto-approve tfplan
    chdir: "{{ terraform_work_dir }}"
  register: terraform_apply
  when: terraform_plan.changed

- name: Get Terraform outputs
  ansible.builtin.command:
    cmd: terraform output -json
    chdir: "{{ terraform_work_dir }}"
  register: terraform_outputs
  changed_when: false

- name: Parse Terraform outputs
  ansible.builtin.set_fact:
    vpc_outputs: "{{ terraform_outputs.stdout | from_json }}"
  when: terraform_outputs.stdout != ""

- name: Display VPC creation results
  ansible.builtin.debug:
    msg:
      - "VPC ID: {{ vpc_outputs.vpc_id.value if vpc_outputs is defined else 'Not available' }}"
      - "VPC CIDR: {{ vpc_outputs.vpc_cidr_block.value if vpc_outputs is defined else 'Not available' }}"
      - "Public Subnets: {{ vpc_outputs.public_subnet_ids.value if vpc_outputs is defined else 'Not available' }}"
      - "Private Subnets: {{ vpc_outputs.private_subnet_ids.value if vpc_outputs is defined else 'Not available' }}"
  when: vpc_outputs is defined

- name: Clean up Terraform plan file
  ansible.builtin.file:
    path: "{{ terraform_work_dir }}/tfplan"
    state: absent
