---
# Create S3 bucket for Terraform state storage

- name: Check if S3 bucket exists
  amazon.aws.s3_bucket_info:
    name_filter:
      - "{{ terraform_state_bucket }}"
  register: bucket_info
  failed_when: false  # Avoid task failure if bucket doesn't exist

- name: Debug bucket_info
  ansible.builtin.debug:
    var: bucket_info

- name: Validate terraform_state_bucket
  ansible.builtin.fail:
    msg: "Variable 'terraform_state_bucket' is not set or is less than 3 characters."
  when: terraform_state_bucket is not defined or terraform_state_bucket | length < 3

- name: Debug bucket name
  ansible.builtin.debug:
    msg: "Creating bucket: {{ terraform_state_bucket }}"

- name: Create S3 bucket for Terraform state
  amazon.aws.s3_bucket:
    name: "{{ terraform_state_bucket }}"
    state: present
    region: "{{ terraform_state_region }}"
    encryption: "AES256"
  register: bucket_creation


- name: Enable S3 bucket encryption
  amazon.aws.s3_bucket:
    name: "{{ terraform_state_bucket }}"
    region: "{{ terraform_state_region }}"
    encryption: "AES256"
  when: bucket_creation.changed or (bucket_info.buckets | length == 0)

- name: Block public access to S3 bucket
  amazon.aws.s3_bucket:
    name: "{{ terraform_state_bucket }}"
    region: "{{ terraform_state_region }}"
    public_access:
      block_public_acls: true
      block_public_policy: true
      ignore_public_acls: tr
