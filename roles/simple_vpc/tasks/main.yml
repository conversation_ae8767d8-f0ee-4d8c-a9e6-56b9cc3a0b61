---
# Main tasks file for simple_vpc role

- name: Validate required variables
  ansible.builtin.assert:
    that:
      - terraform_state_bucket is defined
    fail_msg: "terraform_state_bucket must be defined and not empty"
    success_msg: "Required variables are properly defined"

- name: Display role configuration
  ansible.builtin.debug:
    msg:
      - "VPC Name: {{ vpc_name }}"
      - "VPC CIDR: {{ vpc_cidr }}"
      - "AWS Region: {{ aws_region }}"
      - "Environment: {{ environment }}"
      - "Terraform State Bucket: {{ terraform_state_bucket }}"
      - "Terraform State Key: {{ terraform_state_key }}"

- name: Create S3 bucket for Terraform state
  ansible.builtin.include_tasks: create_s3_bucket.yml

- name: Deploy VPC using Terraform
  ansible.builtin.include_tasks: deploy_vpc.yml
