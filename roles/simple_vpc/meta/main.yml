---
# Role metadata for simple_vpc role

galaxy_info:
  author: Simple VPC Role
  description: Simple AWS VPC creation using Terraform with S3 backend
  company: Custom
  license: MIT
  min_ansible_version: "2.12.0"
  
  platforms:
    - name: EL
      versions:
        - "8"
        - "9"
    - name: Ubuntu
      versions:
        - focal
        - jammy
    - name: Debian
      versions:
        - bullseye
        - bookworm

  galaxy_tags:
    - aws
    - vpc
    - terraform
    - networking
    - infrastructure

# No dependencies - self-contained role
dependencies: []

# Role configuration
allow_duplicates: false
