terraform {
  required_version = ">= 1.0"

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "{{ aws_provider_version }}"
    }
  }
}

provider "aws" {
  region = "{{ aws_region }}"
}

module "simple_vpc" {
  source = "./modules/simple_vpc"

  vpc_name               = "{{ vpc_name }}"
  vpc_cidr              = "{{ vpc_cidr }}"
  public_subnet_cidrs   = {{ public_subnet_cidrs | to_json }}
  private_subnet_cidrs  = {{ private_subnet_cidrs | to_json }}
  availability_zones    = {{ availability_zones | to_json }}
  environment           = "{{ environment }}"
  common_tags           = {{ common_tags | to_json }}
}
