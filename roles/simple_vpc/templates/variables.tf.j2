# Variables file for Terraform configuration
# This file is generated by Ansible and contains the variables passed to the module

variable "vpc_name" {
  description = "Name of the VPC"
  type        = string
  default     = "{{ vpc_name }}"
}

variable "vpc_cidr" {
  description = "CIDR block for VPC"
  type        = string
  default     = "{{ vpc_cidr }}"
}

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "{{ environment }}"
}
