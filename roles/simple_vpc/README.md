# Simple VPC Role (simple_vpc)

This role creates a simple AWS VPC using Terraform with S3 backend for state storage. It integrates Terraform and Ansible without using DynamoDB for state locking and uses only role-specific variables.

## Features

- Creates a VPC with public and private subnets
- Sets up Internet Gateway and NAT Gateway
- Configures route tables
- Uses S3 backend for Terraform state storage (no DynamoDB)
- Self-contained with role-specific variables only
- No dependencies on common roles

## Requirements

- Terraform >= 1.0
- Ansible >= 2.12
- AWS CLI configured with appropriate credentials
- S3 bucket for Terraform state storage

## Role Variables

All variables are defined within the role and can be overridden:

- `vpc_name`: Name of the VPC (default: "simple-vpc")
- `vpc_cidr`: CIDR block for VPC (default: "10.0.0.0/16")
- `aws_region`: AWS region (default: "us-east-1")
- `environment`: Environment tag (default: "dev")
- `terraform_state_bucket`: S3 bucket for state storage
- `terraform_state_key`: S3 key for state file

## Example Usage

```yaml
- hosts: localhost
  roles:
    - role: simple_vpc
      vars:
        vpc_name: "my-simple-vpc"
        vpc_cidr: "********/16"
        aws_region: "us-west-2"
        environment: "production"
        terraform_state_bucket: "my-terraform-state-bucket"
```

## Directory Structure

```
roles/simple_vpc/
├── README.md
├── meta/
│   └── main.yml
├── vars/
│   └── main.yml
├── tasks/
│   ├── main.yml
│   ├── create_s3_bucket.yml
│   └── deploy_vpc.yml
├── templates/
│   ├── main.tf.j2
│   ├── variables.tf.j2
│   ├── outputs.tf.j2
│   └── backend.tf.j2
└── terraform/
    └── modules/
        └── simple_vpc/
            ├── main.tf
            ├── variables.tf
            ├── outputs.tf
            └── versions.tf
```
