---
# Default variables for simple_vpc role
# These can be overridden when calling the role

# VPC Configuration
vpc_name: "simple-vpc"
vpc_cidr: "10.0.0.0/16"

# Subnet Configuration
public_subnet_cidrs:
  - "10.0.1.0/24"
  - "10.0.2.0/24"

private_subnet_cidrs:
  - "10.0.10.0/24"
  - "10.0.20.0/24"

# AWS Configuration
aws_region: "us-east-1"
availability_zones:
  - "us-east-1a"
  - "us-east-1b"

# Environment and Tagging
environment: "dev"
project_name: "simple-vpc-project"

# Terraform Configuration
terraform_version: "~> 1.0"
aws_provider_version: "~> 5.0"

# Local state configuration (no S3 backend)
use_local_state: true

# Working Directory
terraform_work_dir: "/tmp/simple_vpc_terraform"

# Common Tags
common_tags:
  Environment: "{{ environment }}"
  Project: "{{ project_name }}"
  ManagedBy: "Terraform"
  Role: "simple_vpc"
