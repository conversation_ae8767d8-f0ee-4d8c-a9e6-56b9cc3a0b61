---
# Minimal example playbook for deploying a simple VPC
# This shows the minimum required configuration

- name: Deploy Simple VPC with Minimal Configuration
  hosts: localhost
  gather_facts: false
  # No variables required - uses all defaults

  tasks:
    - name: Deploy VPC with default settings
      ansible.builtin.include_role:
        name: simple_vpc

    - name: Show deployment info
      ansible.builtin.debug:
        msg: "VPC deployed with default configuration in {{ aws_region | default('us-east-1') }}"
