---
# Minimal example playbook for deploying a simple VPC
# This shows the minimum required configuration

- name: Deploy Simple VPC with Minimal Configuration
  hosts: localhost
  gather_facts: false
  vars:
    # Only required variable - S3 bucket for Terraform state
    terraform_state_bucket: "my-minimal-vpc-state-bucket"

  tasks:
    - name: Deploy VPC with default settings
      ansible.builtin.include_role:
        name: simple_vpc

    - name: Show deployment info
      ansible.builtin.debug:
        msg: "VPC deployed with default configuration in {{ aws_region | default('us-east-1') }}"
