---
# Example playbook for deploying a simple VPC using the simple_vpc role
# This playbook demonstrates how to use the simple_vpc role with custom variables

- name: Deploy Simple VPC Infrastructure
  hosts: localhost
  gather_facts: false
  vars:
    # Optional: Override default VPC configuration
    vpc_name: "my-simple-vpc"
    vpc_cidr: "********/16"
    aws_region: "us-east-1"
    environment: "development"
    project_name: "my-project"
    
    # Optional: Override subnet configuration
    public_subnet_cidrs:
      - "********/24"
      - "********/24"
    
    private_subnet_cidrs:
      - "*********/24"
      - "*********/24"
    
    availability_zones:
      - "us-east-1a"
      - "us-east-1b"
    
    # Optional: Custom tags
    common_tags:
      Environment: "{{ environment }}"
      Project: "{{ project_name }}"
      ManagedBy: "Terraform"
      Role: "simple_vpc"
      Owner: "DevOps Team"
      CostCenter: "Engineering"

  tasks:
    - name: Deploy VPC using simple_vpc role
      ansible.builtin.include_role:
        name: simple_vpc

    - name: Display completion message
      ansible.builtin.debug:
        msg:
          - "VPC deployment completed successfully!"
          - "VPC Name: {{ vpc_name }}"
          - "Region: {{ aws_region }}"
          - "Environment: {{ environment }}"
          - "Terraform state stored locally in: {{ terraform_work_dir | default('/tmp/simple_vpc_terraform') }}"
