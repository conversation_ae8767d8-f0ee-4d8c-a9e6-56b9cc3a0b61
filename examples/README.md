# Simple VPC Examples

This directory contains example playbooks demonstrating how to use the `simple_vpc` role.

## Prerequisites

1. **AWS CLI configured** with appropriate credentials
2. **Terraform installed** (version >= 1.0)
3. **Ansible installed** (version >= 2.12)
4. **AWS Ansible collections** installed:
   ```bash
   ansible-galaxy collection install amazon.aws
   ```

## Examples

### 1. Minimal VPC Deployment (`minimal_vpc_playbook.yml`)

The simplest way to deploy a VPC with default settings:

```bash
ansible-playbook examples/minimal_vpc_playbook.yml
```

This creates:
- VPC with CIDR `10.0.0.0/16`
- 2 public subnets and 2 private subnets
- Internet Gateway and NAT Gateways
- Appropriate route tables

### 2. Custom VPC Deployment (`simple_vpc_playbook.yml`)

Deploy a VPC with custom configuration:

```bash
ansible-playbook examples/simple_vpc_playbook.yml
```

This example shows how to:
- Customize VPC name and CIDR
- Override subnet configurations
- Set custom tags
- Specify different AWS region

## Required Variables

The only required variable is:
- `terraform_state_bucket`: S3 bucket name for storing Terraform state

## Optional Variables

You can override any of the default variables defined in `roles/simple_vpc/vars/main.yml`:

- `vpc_name`: Name of the VPC
- `vpc_cidr`: CIDR block for the VPC
- `aws_region`: AWS region for deployment
- `environment`: Environment tag
- `public_subnet_cidrs`: List of public subnet CIDRs
- `private_subnet_cidrs`: List of private subnet CIDRs
- `availability_zones`: List of availability zones
- `common_tags`: Additional tags to apply

## Running the Examples

1. **Set your S3 bucket name** in the playbook variables
2. **Ensure AWS credentials** are configured
3. **Run the playbook**:
   ```bash
   ansible-playbook examples/simple_vpc_playbook.yml
   ```

## Cleanup

To destroy the created infrastructure:

1. Navigate to the Terraform working directory (default: `/tmp/simple_vpc_terraform`)
2. Run: `terraform destroy`

Or create a cleanup playbook that runs `terraform destroy` in the working directory.

## Notes

- The S3 bucket for state storage will be created automatically if it doesn't exist
- No DynamoDB table is used for state locking as requested
- All variables are self-contained within the role
- The role has no dependencies on common roles
