---
# Cleanup playbook for destroying VPC infrastructure created by simple_vpc role
# This playbook will destroy the Terraform-managed infrastructure

- name: Cleanup Simple VPC Infrastructure
  hosts: localhost
  gather_facts: false
  vars:
    # Must match the working directory used during deployment
    terraform_work_dir: "/tmp/simple_vpc_terraform"
    
    # No S3 bucket cleanup needed - using local state

  tasks:
    - name: Check if Terraform working directory exists
      ansible.builtin.stat:
        path: "{{ terraform_work_dir }}"
      register: terraform_dir

    - name: Destroy Terraform infrastructure
      block:
        - name: Run terraform destroy
          ansible.builtin.command:
            cmd: terraform destroy -auto-approve
            chdir: "{{ terraform_work_dir }}"
          register: terraform_destroy

        - name: Display destruction results
          ansible.builtin.debug:
            msg: "Infrastructure destroyed successfully"

        - name: Clean up Terraform working directory
          ansible.builtin.file:
            path: "{{ terraform_work_dir }}"
            state: absent

      when: terraform_dir.stat.exists

    - name: Display cleanup completion
      ansible.builtin.debug:
        msg:
          - "VPC cleanup completed!"
          - "Terraform directory removed: {{ terraform_dir.stat.exists }}"
          - "Local state files cleaned up"
