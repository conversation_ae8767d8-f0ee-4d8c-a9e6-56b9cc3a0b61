---
# Cleanup playbook for destroying VPC infrastructure created by simple_vpc role
# This playbook will destroy the Terraform-managed infrastructure

- name: Cleanup Simple VPC Infrastructure
  hosts: localhost
  gather_facts: false
  vars:
    # Must match the working directory used during deployment
    terraform_work_dir: "/tmp/simple_vpc_terraform"
    
    # Optional: Set to true to also delete the S3 bucket (WARNING: This will delete all state files)
    delete_s3_bucket: false
    terraform_state_bucket: ""  # Set this if you want to delete the bucket

  tasks:
    - name: Check if Terraform working directory exists
      ansible.builtin.stat:
        path: "{{ terraform_work_dir }}"
      register: terraform_dir

    - name: Destroy Terraform infrastructure
      block:
        - name: Run terraform destroy
          ansible.builtin.command:
            cmd: terraform destroy -auto-approve
            chdir: "{{ terraform_work_dir }}"
          register: terraform_destroy

        - name: Display destruction results
          ansible.builtin.debug:
            msg: "Infrastructure destroyed successfully"

        - name: Clean up Terraform working directory
          ansible.builtin.file:
            path: "{{ terraform_work_dir }}"
            state: absent

      when: terraform_dir.stat.exists

    - name: Delete S3 bucket (optional)
      amazon.aws.s3_bucket:
        name: "{{ terraform_state_bucket }}"
        state: absent
        force: true
      when: 
        - delete_s3_bucket | bool
        - terraform_state_bucket != ""
      register: bucket_deletion

    - name: Display cleanup completion
      ansible.builtin.debug:
        msg:
          - "VPC cleanup completed!"
          - "Terraform directory removed: {{ terraform_dir.stat.exists }}"
          - "S3 bucket deleted: {{ bucket_deletion.changed | default(false) }}"

    - name: Warning about manual cleanup
      ansible.builtin.debug:
        msg:
          - "WARNING: If you want to delete the S3 bucket containing Terraform state,"
          - "set delete_s3_bucket: true and provide the terraform_state_bucket name."
          - "This will permanently delete all Terraform state files in the bucket!"
      when: not (delete_s3_bucket | bool)
