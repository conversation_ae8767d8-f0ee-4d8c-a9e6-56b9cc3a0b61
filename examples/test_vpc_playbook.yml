---
# Test playbook to validate the simple_vpc role setup
# This playbook performs dry-run validation without actually creating resources

- name: Test Simple VPC Role Configuration
  hosts: localhost
  gather_facts: true
  vars:
    # Test configuration
    vpc_name: "test-vpc"
    vpc_cidr: "*********/16"
    aws_region: "us-east-1"
    environment: "test"
    
    # Override working directory for testing
    terraform_work_dir: "/tmp/test_simple_vpc_terraform"

  tasks:
    - name: Validate role variables
      ansible.builtin.assert:
        that:
          - terraform_state_bucket is defined
          - terraform_state_bucket != ""
          - vpc_name is defined
          - vpc_cidr is defined
          - aws_region is defined
        fail_msg: "Required variables are not properly defined"
        success_msg: "All required variables are properly defined"

    - name: Test Terraform template generation
      block:
        - name: Create test working directory
          ansible.builtin.file:
            path: "{{ terraform_work_dir }}"
            state: directory
            mode: '0755'

        - name: Test template generation
          ansible.builtin.template:
            src: "{{ role_path }}/templates/{{ item.src }}"
            dest: "{{ terraform_work_dir }}/{{ item.dest }}"
            mode: '0644'
          loop:
            - { src: "main.tf.j2", dest: "main.tf" }
            - { src: "variables.tf.j2", dest: "variables.tf" }
            - { src: "outputs.tf.j2", dest: "outputs.tf" }
            - { src: "backend.tf.j2", dest: "backend.tf" }
          vars:
            role_path: "{{ playbook_dir }}/../roles/simple_vpc"

        - name: Validate generated Terraform files
          ansible.builtin.stat:
            path: "{{ terraform_work_dir }}/{{ item }}"
          register: tf_files
          loop:
            - "main.tf"
            - "variables.tf"
            - "outputs.tf"
            - "backend.tf"

        - name: Check if all Terraform files were generated
          ansible.builtin.assert:
            that:
              - item.stat.exists
            fail_msg: "Terraform file {{ item.item }} was not generated"
            success_msg: "Terraform file {{ item.item }} generated successfully"
          loop: "{{ tf_files.results }}"

        - name: Display generated main.tf content
          ansible.builtin.debug:
            msg: "Generated main.tf file exists and is ready for Terraform"

        - name: Clean up test directory
          ansible.builtin.file:
            path: "{{ terraform_work_dir }}"
            state: absent

      rescue:
        - name: Clean up on failure
          ansible.builtin.file:
            path: "{{ terraform_work_dir }}"
            state: absent

        - name: Fail with error message
          ansible.builtin.fail:
            msg: "Template generation test failed"

    - name: Test role inclusion (dry run)
      ansible.builtin.debug:
        msg:
          - "Role validation completed successfully!"
          - "The simple_vpc role is ready to use with the following configuration:"
          - "  VPC Name: {{ vpc_name }}"
          - "  VPC CIDR: {{ vpc_cidr }}"
          - "  AWS Region: {{ aws_region }}"
          - "  Environment: {{ environment }}"
          - "  State Bucket: {{ terraform_state_bucket }}"

    - name: Display next steps
      ansible.builtin.debug:
        msg:
          - "To deploy the VPC, run:"
          - "  ansible-playbook examples/simple_vpc_playbook.yml"
          - ""
          - "To clean up, run:"
          - "  ansible-playbook examples/cleanup_vpc_playbook.yml"
